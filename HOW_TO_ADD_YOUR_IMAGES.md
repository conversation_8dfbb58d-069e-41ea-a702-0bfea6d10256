# How to Add Your Own Images to Mini Ticket System

## 📁 Image Requirements

### **Logo Images:**
- **Main Logo:** `images/logo.png` (Any size - automatically scaled to fit perfectly!)
- **Header Background:** `images/header_background.jpg` (1920x1080 recommended)
- **Window Icon:** `images/icon.png` (64x64 pixels recommended)

### **Event Images:**
All images should be **200x150 pixels** for best display:

1. `images/rock_concert.jpg` - Rock Concert 2025
2. `images/classical_music.jpg` - Classical Music Evening
3. `images/jazz_festival.jpg` - Jazz Festival
4. `images/pop_concert.jpg` - Pop Music Concert
5. `images/comedy_show.jpg` - Comedy Show
6. `images/theater_play.jpg` - Theater Play
7. `images/magic_show.jpg` - Magic Show
8. `images/basketball.jpg` - Basketball Championship
9. `images/football.jpg` - Football Match
10. `images/art_exhibition.jpg` - Art Exhibition Opening
11. `images/food_festival.jpg` - Food Festival
12. `images/tech_conference.jpg` - Technology Conference

## 🖼️ How to Replace Images

### **Method 1: Simple Replacement**
1. **Find your images** on your computer
2. **Rename them** to match the exact filenames above
3. **Copy them** to the `images/` folder in your project
4. **Replace the existing files** when prompted

### **Method 2: Using Different Filenames**
If you want to use different filenames, you need to update the code:

1. **Add your images** to the `images/` folder with any names
2. **Update BookingController.java** - change the image paths:

```java
// In BookingController.java, line 29-59, change the image paths:
events.add(new Event(1, "Rock Concert 2025", "2025-02-15", "City Arena",
    "Amazing rock concert...", new BigDecimal("50.00"), 500, "images/YOUR_ROCK_IMAGE.jpg"));
```

3. **Update HomePanel.java** for logo (lines 94-96):
```java
ImageIcon logoIcon = new ImageIcon("images/YOUR_LOGO.png");
```

4. **Update MainFrame.java** for icon (lines 42-43):
```java
setIconImage(Toolkit.getDefaultToolkit().createImage("images/YOUR_ICON.png"));
```

## 📐 Image Size Guidelines

### **Recommended Sizes:**
- **Event Images:** 200x150 pixels (4:3 aspect ratio)
- **Logo:** 200x80 pixels (flexible)
- **Icon:** 64x64 pixels (square)

### **Supported Formats:**
- **JPG/JPEG** - Best for photos
- **PNG** - Best for logos with transparency
- **GIF** - Basic support

## 🎨 Image Tips for Professional Look

### **Event Images:**
- Use **high-quality photos** related to each event type
- Ensure **good lighting** and **clear subjects**
- **Crop to 4:3 ratio** (200x150) for consistency
- Use **vibrant, engaging images** that represent the event

### **Logo Design:**
- **Any size works** - the system automatically scales your logo to fit perfectly
- **Maintains aspect ratio** - no distortion of your logo
- Use **contrasting colors** for visibility against the header background
- Include your **project name** or **initials**
- **PNG format recommended** for logos with transparency
- **High resolution** logos will look crisp when scaled down

### **Icon Design:**
- Use a **simplified version** of your logo
- Ensure it's **recognizable at 64x64 pixels**
- Use **bold, simple shapes**

## 🔧 Quick Setup Steps

1. **Create/Find Your Images:**
   - Logo: Design or find a suitable logo image
   - Icon: Create a small square version of your logo
   - Events: Find 12 appropriate images for different event types

2. **Prepare Images:**
   - Resize to recommended dimensions
   - Save in appropriate formats (JPG for photos, PNG for logos)
   - Name them according to the list above

3. **Replace in Project:**
   - Copy all images to the `images/` folder
   - Overwrite existing placeholder images
   - Test the application to ensure images load correctly

## 🚀 Testing Your Images

1. **Run the application:**
   ```bash
   javac *.java && java Main
   ```

2. **Check that:**
   - Logo appears in the header
   - Window icon shows in taskbar
   - All 12 event images display properly
   - Images are properly sized and not distorted

3. **If images don't appear:**
   - Check file names match exactly (case-sensitive)
   - Verify images are in the `images/` folder
   - Ensure image files aren't corrupted
   - Check file permissions

## 💡 Pro Tips

- **Use consistent style** across all event images
- **Optimize file sizes** - keep images under 500KB each
- **Test on different screens** to ensure images look good
- **Keep backup copies** of your original images
- **Consider image licensing** if using photos from the internet

## 🎯 For Your Presentation

Having **real, professional images** will make your application look much more impressive to your lecturer. It shows attention to detail and makes the system feel like a real application rather than just a demo.

**Good luck with your project!** 🎉
