
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Receipt Panel for Mini Ticket System
 * Academic project for BIT 4043 OOP course
 * Displays booking confirmation and receipt details
 */
public class ReceiptPanel extends JPanel {
    private Main mainFrame;
    private BookingController bookingController;
    private JTextArea receiptTextArea;
    private JButton homeButton;
    private JButton newBookingButton;

    public ReceiptPanel(Main mainFrame, BookingController bookingController) {
        this.mainFrame = mainFrame;
        this.bookingController = bookingController;
        initializePanel();
    }

    private void initializePanel() {
        setLayout(new BorderLayout());
        setBackground(Color.WHITE);

        // Create header
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // Create receipt display area
        JPanel receiptPanel = createReceiptPanel();
        add(receiptPanel, BorderLayout.CENTER);

        // Create button panel
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(Main.SUCCESS_GREEN);
        headerPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        JLabel titleLabel = new JLabel("Booking Confirmation", JLabel.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 32));
        titleLabel.setForeground(Color.WHITE);

        JLabel subtitleLabel = new JLabel("Your tickets have been successfully booked!", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Segoe UI", Font.PLAIN, 18));
        subtitleLabel.setForeground(new Color(230, 255, 230));

        JPanel titlePanel = new JPanel(new GridLayout(2, 1, 10, 10));
        titlePanel.setBackground(Main.SUCCESS_GREEN);
        titlePanel.add(titleLabel);
        titlePanel.add(subtitleLabel);

        headerPanel.add(titlePanel, BorderLayout.CENTER);
        return headerPanel;
    }

    private JPanel createReceiptPanel() {
        JPanel receiptPanel = new JPanel(new BorderLayout());
        receiptPanel.setBackground(Color.WHITE);
        receiptPanel.setBorder(BorderFactory.createEmptyBorder(30, 50, 30, 50));

        // Receipt title
        JLabel receiptTitle = new JLabel("BOOKING RECEIPT", JLabel.CENTER);
        receiptTitle.setFont(new Font("Segoe UI", Font.BOLD, 20));
        receiptTitle.setForeground(new Color(33, 37, 41));
        receiptTitle.setBorder(BorderFactory.createEmptyBorder(0, 0, 25, 0));

        // Receipt text area
        receiptTextArea = new JTextArea();
        receiptTextArea.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        receiptTextArea.setEditable(false);
        receiptTextArea.setBackground(new Color(248, 249, 250));
        receiptTextArea.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(220, 220, 220), 1),
            BorderFactory.createEmptyBorder(25, 25, 25, 25)
        ));

        JScrollPane scrollPane = new JScrollPane(receiptTextArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new Dimension(600, 300));

        receiptPanel.add(receiptTitle, BorderLayout.NORTH);
        receiptPanel.add(scrollPane, BorderLayout.CENTER);

        return receiptPanel;
    }

    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(new Color(240, 240, 240));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        homeButton = new JButton("Back to Home");
        homeButton.setPreferredSize(new Dimension(150, 50));
        homeButton.setBackground(Main.TEXT_LIGHT);
        homeButton.setForeground(Color.WHITE);
        homeButton.setFont(new Font("Segoe UI", Font.BOLD, 15));
        homeButton.setOpaque(true);
        homeButton.setBorderPainted(false);
        homeButton.setFocusPainted(false);
        homeButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        homeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainFrame.showHome();
            }
        });

        newBookingButton = new JButton("Book Another Event");
        newBookingButton.setPreferredSize(new Dimension(180, 50));
        newBookingButton.setBackground(Main.ACCENT_BLUE);
        newBookingButton.setForeground(Color.WHITE);
        newBookingButton.setFont(new Font("Segoe UI", Font.BOLD, 15));
        newBookingButton.setOpaque(true);
        newBookingButton.setBorderPainted(false);
        newBookingButton.setFocusPainted(false);
        newBookingButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        newBookingButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainFrame.showHome();
            }
        });

        buttonPanel.add(homeButton);
        buttonPanel.add(Box.createHorizontalStrut(20));
        buttonPanel.add(newBookingButton);

        return buttonPanel;
    }

    public void displayBooking(Booking booking) {
        if (booking == null) {
            receiptTextArea.setText("No booking information available.");
            return;
        }

        StringBuilder receipt = new StringBuilder();
        receipt.append("===============================================\n");
        receipt.append("           MINI TICKET SYSTEM\n");
        receipt.append("         BIT 4043 OOP PROJECT\n");
        receipt.append("===============================================\n\n");

        receipt.append("BOOKING DETAILS:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Booking ID:      #%d\n", booking.getId()));
        receipt.append(String.format("Booking Date:    %s\n", booking.getBookingDate()));
        receipt.append("\n");

        receipt.append("CUSTOMER INFORMATION:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Name:            %s\n", booking.getCustomerName()));
        receipt.append(String.format("Email:           %s\n", booking.getCustomerEmail()));
        if (booking.getCustomerPhone() != null && !booking.getCustomerPhone().trim().isEmpty()) {
            receipt.append(String.format("Phone:           %s\n", booking.getCustomerPhone()));
        }
        receipt.append("\n");

        receipt.append("EVENT INFORMATION:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Event:           %s\n", booking.getEvent().getName()));
        receipt.append(String.format("Date:            %s\n", booking.getEvent().getDate()));
        receipt.append(String.format("Venue:           %s\n", booking.getEvent().getVenue()));
        receipt.append(String.format("Description:     %s\n", booking.getEvent().getDescription()));
        receipt.append("\n");

        receipt.append("TICKET INFORMATION:\n");
        receipt.append("-----------------------------------------------\n");
        receipt.append(String.format("Seat Type:       %s\n", booking.getSeatType()));
        receipt.append(String.format("Quantity:        %d ticket(s)\n", booking.getQuantity()));
        receipt.append(String.format("Base Price:      $%.2f per ticket\n", booking.getEvent().getBasePrice()));
        receipt.append(String.format("Total Amount:    $%.2f\n", booking.getTotalPrice()));
        receipt.append("\n");

        receipt.append("===============================================\n");
        receipt.append("Thank you for using Mini Ticket System!\n");
        receipt.append("Please keep this receipt for your records.\n");
        receipt.append("===============================================\n");

        receiptTextArea.setText(receipt.toString());
        receiptTextArea.setCaretPosition(0); // Scroll to top
    }
}
