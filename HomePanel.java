
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.util.ArrayList;

/**
 * Home Panel for Mini Ticket System
 * Academic project for BIT 4043 OOP course
 * Displays system information and available events
 */
public class HomePanel extends JPanel {
    private Main mainFrame;
    private BookingController bookingController;
    private JPanel eventsPanel;
    private JScrollPane scrollPane;

    public HomePanel(Main mainFrame, BookingController bookingController) {
        this.mainFrame = mainFrame;
        this.bookingController = bookingController;
        initializePanel();
    }

    private void initializePanel() {
        setLayout(new BorderLayout());
        setBackground(Color.WHITE);

        // Create header panel
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // Create events panel
        createEventsPanel();
        add(scrollPane, BorderLayout.CENTER);

        // Create footer panel
        JPanel footerPanel = createFooterPanel();
        add(footerPanel, BorderLayout.SOUTH);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        // Beautiful professional blue header
        headerPanel.setBackground(Main.PRIMARY_DARK);
        headerPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        // Logo panel (left side)
        JPanel logoPanel = createLogoPanel();
        headerPanel.add(logoPanel, BorderLayout.WEST);

        // Title panel (center)
        JPanel titlePanel = new JPanel(new GridLayout(3, 1, 10, 10));
        titlePanel.setBackground(Main.PRIMARY_DARK);

        // Title
        JLabel titleLabel = new JLabel("Mini Ticket Booking System", JLabel.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 36));
        titleLabel.setForeground(Color.WHITE);

        // Subtitle
        JLabel subtitleLabel = new JLabel("BIT 4043 Object-Oriented Programming Project", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Segoe UI", Font.ITALIC, 16));
        subtitleLabel.setForeground(new Color(189, 195, 199));

        // Welcome message
        JLabel welcomeLabel = new JLabel("Welcome! Browse our exciting events and book your tickets.", JLabel.CENTER);
        welcomeLabel.setFont(new Font("Segoe UI", Font.PLAIN, 18));
        welcomeLabel.setForeground(Main.ACCENT_BLUE);

        titlePanel.add(titleLabel);
        titlePanel.add(subtitleLabel);
        titlePanel.add(welcomeLabel);

        headerPanel.add(titlePanel, BorderLayout.CENTER);
        return headerPanel;
    }

    /**
     * Create logo panel for the header
     */
    private JPanel createLogoPanel() {
        JPanel logoPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        logoPanel.setBackground(Main.PRIMARY_DARK);
        logoPanel.setPreferredSize(new Dimension(250, 120));

        JLabel logoLabel = new JLabel();
        logoLabel.setHorizontalAlignment(JLabel.CENTER);
        logoLabel.setVerticalAlignment(JLabel.CENTER);

        try {
            // Try to load the logo
            File logoFile = new File("images/logo.png");
            if (logoFile.exists()) {
                ImageIcon logoIcon = new ImageIcon("images/logo.png");
                logoLabel.setIcon(logoIcon);
            } else {
                // Fallback to text logo
                logoLabel.setText("<html><div style='text-align: center;'>" +
                                "<div style='font-size: 24px; color: white;'>🎫</div>" +
                                "<div style='font-size: 14px; color: white; font-weight: bold;'>MINI TICKET</div>" +
                                "<div style='font-size: 12px; color: lightgray;'>SYSTEM</div>" +
                                "</html>");
                logoLabel.setHorizontalAlignment(JLabel.CENTER);
            }
        } catch (Exception e) {
            // Fallback to text logo
            logoLabel.setText("<html><div style='text-align: center;'>" +
                            "<div style='font-size: 24px; color: white;'>🎫</div>" +
                            "<div style='font-size: 14px; color: white; font-weight: bold;'>MINI TICKET</div>" +
                            "<div style='font-size: 12px; color: lightgray;'>SYSTEM</div>" +
                            "</html>");
            logoLabel.setHorizontalAlignment(JLabel.CENTER);
        }

        logoPanel.add(logoLabel);
        return logoPanel;
    }

    private void createEventsPanel() {
        eventsPanel = new JPanel();
        eventsPanel.setLayout(new BoxLayout(eventsPanel, BoxLayout.Y_AXIS));
        eventsPanel.setBackground(Color.WHITE);
        eventsPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Add title for events section
        JLabel eventsTitle = new JLabel("Available Events");
        eventsTitle.setFont(new Font("Arial", Font.BOLD, 20));
        eventsTitle.setAlignmentX(Component.CENTER_ALIGNMENT);
        eventsPanel.add(eventsTitle);
        eventsPanel.add(Box.createVerticalStrut(20));

        // Load events
        refreshEventList();

        scrollPane = new JScrollPane(eventsPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
    }

    public void refreshEventList() {
        // Remove existing event panels (keep title)
        Component[] components = eventsPanel.getComponents();
        for (int i = 2; i < components.length; i++) {
            eventsPanel.remove(components[i]);
        }

        ArrayList<Event> events = bookingController.getAllEvents();

        for (Event event : events) {
            JPanel eventPanel = createEventPanel(event);
            eventsPanel.add(eventPanel);
            eventsPanel.add(Box.createVerticalStrut(15));
        }

        eventsPanel.revalidate();
        eventsPanel.repaint();
    }

    private JPanel createEventPanel(Event event) {
        JPanel eventPanel = new JPanel(new BorderLayout());
        // Beautiful card design with professional styling
        eventPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(220, 221, 225), 1),
            BorderFactory.createEmptyBorder(8, 8, 8, 8)
        ));
        eventPanel.setBackground(Main.CARD_WHITE);
        eventPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 250));

        // Create image panel
        JPanel imagePanel = createImagePanel(event);
        eventPanel.add(imagePanel, BorderLayout.WEST);

        // Create content panel (details + button)
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(Main.CARD_WHITE);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(25, 25, 25, 25));

        // Event details panel
        JPanel detailsPanel = new JPanel();
        detailsPanel.setLayout(new BoxLayout(detailsPanel, BoxLayout.Y_AXIS));
        detailsPanel.setBackground(Main.CARD_WHITE);

        // Event name
        JLabel nameLabel = new JLabel(event.getName());
        nameLabel.setFont(new Font("Segoe UI", Font.BOLD, 22));
        nameLabel.setForeground(Main.TEXT_DARK);
        nameLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        // Event description
        JLabel descLabel = new JLabel("<html><div style='width: 400px; line-height: 1.5;'>" + event.getDescription() + "</div></html>");
        descLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        descLabel.setForeground(Main.TEXT_LIGHT);
        descLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        // Date info panel
        JPanel datePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 3));
        datePanel.setBackground(Main.CARD_WHITE);
        datePanel.setAlignmentX(Component.LEFT_ALIGNMENT);

        JLabel dateIcon = new JLabel("📅");
        dateIcon.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        JLabel dateLabel = new JLabel(event.getDate());
        dateLabel.setFont(new Font("Segoe UI", Font.PLAIN, 15));
        dateLabel.setForeground(Main.PRIMARY_BLUE);

        datePanel.add(dateIcon);
        datePanel.add(dateLabel);

        // Venue info panel
        JPanel venuePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 3));
        venuePanel.setBackground(Main.CARD_WHITE);
        venuePanel.setAlignmentX(Component.LEFT_ALIGNMENT);

        JLabel venueIcon = new JLabel("📍");
        venueIcon.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        JLabel venueLabel = new JLabel(event.getVenue());
        venueLabel.setFont(new Font("Segoe UI", Font.PLAIN, 15));
        venueLabel.setForeground(Main.PRIMARY_BLUE);

        venuePanel.add(venueIcon);
        venuePanel.add(venueLabel);

        // Price panel - separate lines for better visibility
        JPanel pricePanel = new JPanel();
        pricePanel.setLayout(new BoxLayout(pricePanel, BoxLayout.Y_AXIS));
        pricePanel.setBackground(Main.CARD_WHITE);
        pricePanel.setAlignmentX(Component.LEFT_ALIGNMENT);

        // Debug: Print price information
        System.out.println("Event: " + event.getName() + ", Base Price: " + event.getBasePrice());

        JLabel priceLabel = new JLabel("Starting from: $" + event.getBasePrice());
        priceLabel.setFont(new Font("Segoe UI", Font.BOLD, 18));
        priceLabel.setForeground(Main.SUCCESS_GREEN);
        priceLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        JLabel seatsLabel = new JLabel(event.getAvailableSeats() + " seats available");
        seatsLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        seatsLabel.setForeground(Main.TEXT_LIGHT);
        seatsLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        pricePanel.add(priceLabel);
        pricePanel.add(Box.createVerticalStrut(3));
        pricePanel.add(seatsLabel);

        // Add components to details panel with spacing
        detailsPanel.add(nameLabel);
        detailsPanel.add(Box.createVerticalStrut(8));
        detailsPanel.add(descLabel);
        detailsPanel.add(Box.createVerticalStrut(10));
        detailsPanel.add(datePanel);
        detailsPanel.add(Box.createVerticalStrut(5));
        detailsPanel.add(venuePanel);
        detailsPanel.add(Box.createVerticalStrut(8));
        detailsPanel.add(pricePanel);

        // Book button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBackground(Main.CARD_WHITE);

        JButton bookButton = new JButton("Book Tickets");
        bookButton.setPreferredSize(new Dimension(160, 50));
        bookButton.setBackground(Main.ACCENT_BLUE);
        bookButton.setForeground(Color.WHITE);
        bookButton.setFont(new Font("Segoe UI", Font.BOLD, 15));
        bookButton.setFocusPainted(false);
        bookButton.setOpaque(true);
        bookButton.setBorderPainted(false);
        bookButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        // Beautiful rounded button effect
        bookButton.setBorder(BorderFactory.createEmptyBorder(15, 30, 15, 30));

        // Add beautiful hover effect
        bookButton.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                bookButton.setBackground(Main.PRIMARY_BLUE);
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                bookButton.setBackground(Main.ACCENT_BLUE);
            }
        });

        bookButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                mainFrame.showBookingForm(event.getId());
            }
        });

        buttonPanel.add(bookButton);

        // Add details and button to content panel
        contentPanel.add(detailsPanel, BorderLayout.CENTER);
        contentPanel.add(buttonPanel, BorderLayout.SOUTH);

        eventPanel.add(contentPanel, BorderLayout.CENTER);

        return eventPanel;
    }

    /**
     * Create image panel for event with fallback to placeholder
     */
    private JPanel createImagePanel(Event event) {
        JPanel imagePanel = new JPanel(new BorderLayout());
        imagePanel.setPreferredSize(new Dimension(200, 220));
        imagePanel.setBackground(new Color(245, 245, 245));
        imagePanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JLabel imageLabel = new JLabel();
        imageLabel.setHorizontalAlignment(JLabel.CENTER);
        imageLabel.setVerticalAlignment(JLabel.CENTER);

        try {
            // Try to load the event image
            File imageFile = new File(event.getImagePath());
            if (imageFile.exists()) {
                ImageIcon originalIcon = new ImageIcon(event.getImagePath());
                Image scaledImage = originalIcon.getImage().getScaledInstance(180, 120, Image.SCALE_SMOOTH);
                ImageIcon scaledIcon = new ImageIcon(scaledImage);
                imageLabel.setIcon(scaledIcon);
            } else {
                // Fallback to placeholder
                createPlaceholderImage(imageLabel, event.getName());
            }
        } catch (Exception e) {
            // If image loading fails, create placeholder
            createPlaceholderImage(imageLabel, event.getName());
        }

        imagePanel.add(imageLabel, BorderLayout.CENTER);
        return imagePanel;
    }

    /**
     * Create a placeholder image when actual image is not available
     */
    private void createPlaceholderImage(JLabel imageLabel, String eventName) {
        // Create a simple colored placeholder
        imageLabel.setText("<html><div style='text-align: center; width: 160px;'>" +
                          "<div style='font-size: 24px; margin-bottom: 10px;'>🎭</div>" +
                          "<div style='font-size: 12px; color: #666;'>" + eventName + "</div>" +
                          "</html>");
        imageLabel.setHorizontalAlignment(JLabel.CENTER);
        imageLabel.setVerticalAlignment(JLabel.CENTER);
        imageLabel.setOpaque(true);
        imageLabel.setBackground(new Color(230, 230, 230));
        imageLabel.setBorder(BorderFactory.createLineBorder(new Color(200, 200, 200), 1));
    }

    private JPanel createFooterPanel() {
        JPanel footerPanel = new JPanel(new FlowLayout());
        footerPanel.setBackground(new Color(240, 240, 240));
        footerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JLabel footerLabel = new JLabel("© 2025 Mini Ticket System - Academic Project");
        footerLabel.setFont(new Font("Arial", Font.ITALIC, 10));
        footerLabel.setForeground(Color.GRAY);

        footerPanel.add(footerLabel);
        return footerPanel;
    }
}
