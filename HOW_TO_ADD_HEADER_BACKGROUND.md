# How to Add Header Background Image

## 📸 Adding a Background Image to the Header

Your Mini Ticket System now supports a beautiful background image in the header section!

### **Quick Setup:**

1. **Find a suitable image** for your header background
   - Recommended: Wide landscape images (16:9 or similar ratio)
   - Good resolution: 1920x1080 or higher
   - Formats supported: JPG, PNG, GIF

2. **Name your image** exactly: `header_background.jpg`

3. **Place the image** in the `images/` folder of your project:
   ```
   MiniTicketSystem/
   ├── images/
   │   ├── header_background.jpg  ← Your background image here
   │   ├── logo.png
   │   └── (other event images...)
   ├── Main.java
   └── (other .java files...)
   ```

### **What Happens:**

✅ **With Background Image:**
- Your image will be scaled to fit the entire header
- A semi-transparent dark overlay is added for text readability
- The logo and title text remain clearly visible

✅ **Without Background Image:**
- Falls back to the original professional blue background
- No changes to functionality

### **Image Recommendations:**

🎯 **Best Image Types:**
- Concert venues, theaters, stadiums
- Event-related scenes (crowds, stages, etc.)
- Abstract patterns or gradients
- City skylines or architectural photos

🎨 **Tips for Best Results:**
- Choose images with darker areas or good contrast
- Avoid images with too much detail in the center
- Horizontal/landscape orientation works best
- Higher resolution = better quality when scaled

### **Technical Details:**

- **File name:** Must be exactly `header_background.jpg`
- **Location:** Must be in the `images/` folder
- **Scaling:** Image automatically scales to fit header dimensions
- **Overlay:** 70% opacity dark overlay ensures text readability
- **Fallback:** Gracefully falls back to solid color if image not found

### **Example Setup:**

1. Download a nice concert hall or event venue image
2. Rename it to `header_background.jpg`
3. Copy it to your `images/` folder
4. Run your application - the header will now have your background!

---

**Note:** The application will automatically detect and use the background image. No code changes needed!
