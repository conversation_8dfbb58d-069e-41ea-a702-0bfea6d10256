# Mini Ticket System - Presentation Preparation Guide
## BIT 4043 Object-Oriented Programming Course

### 📋 Project Overview
**Project Name:** Mini Ticket System
**Purpose:** Academic GUI application demonstrating Java Swing and OOP concepts
**Architecture:** Model-View-Controller (MVC) pattern
**Language:** Java with Swing GUI framework

---

## 🏗️ Project Structure & Files

### Core Java Files:
1. **Main.java** - Application entry point
2. **MainFrame.java** - Main window container
3. **HomePanel.java** - Home screen with event listings
4. **BookingFormPanel.java** - Ticket booking form
5. **ReceiptPanel.java** - Booking confirmation display
6. **Event.java** - Event data model
7. **Booking.java** - Booking data model
8. **BookingController.java** - Business logic controller

### Supporting Files:
- **images/** - Directory containing event images
- **README.md** - Project documentation

---

## 🎯 Key Java Concepts Demonstrated

### 1. Object-Oriented Programming (OOP)
- **Encapsulation:** Private fields with public getters/setters
- **Inheritance:** Panels extend JPanel
- **Polymorphism:** ActionListener implementations
- **Abstraction:** Separation of concerns between classes

### 2. Java Swing Components Used
- **JFrame** - Main application window
- **JPanel** - Container panels
- **JLabel** - Text and image display
- **JButton** - Interactive buttons
- **JTextField** - Text input fields
- **JComboBox** - Dropdown selections
- **JSpinner** - Numeric input
- **JScrollPane** - Scrollable content
- **BorderLayout, FlowLayout, GridLayout, BoxLayout** - Layout managers

### 3. Design Patterns
- **MVC (Model-View-Controller):** Clear separation of data, presentation, and logic
- **Observer Pattern:** Event listeners for user interactions

---

## 📝 Detailed Code Explanation Guide

### Main.java - Application Entry Point
```java
public static void main(String[] args)
```
**Purpose:** Starts the application
**Key Concepts:**
- **SwingUtilities.invokeLater()** - Ensures GUI runs on Event Dispatch Thread
- **UIManager.setLookAndFeel()** - Sets system appearance
- **Exception handling** - Try-catch blocks for error management

**Questions to Expect:**
- Q: "Why use SwingUtilities.invokeLater()?"
- A: "It ensures thread safety by running GUI code on the Event Dispatch Thread"

### Event.java - Data Model
**Purpose:** Represents an event with all its properties
**Key Concepts:**
- **Private fields** - Data encapsulation
- **Constructor** - Object initialization
- **Getters/Setters** - Controlled access to data
- **Business methods** - hasAvailableSeats(), bookSeats()

**Important Fields:**
- `id` - Unique identifier
- `name` - Event name
- `date` - Event date
- `venue` - Location
- `description` - Event details
- `basePrice` - Starting price
- `totalSeats/availableSeats` - Capacity management
- `imagePath` - Image file location

### HomePanel.java - Event Display
**Purpose:** Shows available events with images and details
**Key Concepts:**
- **BorderLayout** - Main panel layout
- **BoxLayout** - Vertical event listing
- **Image handling** - Loading and scaling images
- **Event listeners** - Button click handling

**Important Methods:**
- `createEventPanel()` - Creates individual event cards
- `createImagePanel()` - Handles event images with fallbacks
- `refreshEventList()` - Updates event display

### BookingFormPanel.java - User Input
**Purpose:** Collects customer information and booking details
**Key Concepts:**
- **Form validation** - Input checking
- **JComboBox** - Dropdown for seat types
- **JSpinner** - Quantity selection
- **Price calculation** - Dynamic pricing based on selections

### BookingController.java - Business Logic
**Purpose:** Manages events and bookings (in-memory storage)
**Key Concepts:**
- **ArrayList** - Dynamic data storage
- **Business logic** - Price calculations, seat management
- **Data initialization** - Sample events creation

---

## 🎨 GUI Design Features

### Professional Appearance:
- **Color scheme** - Blue header, white content, subtle borders
- **Typography** - Different font sizes and weights for hierarchy
- **Icons** - Emoji icons for visual appeal (📅 📍)
- **Hover effects** - Button color changes on mouse over
- **Spacing** - Proper margins and padding
- **Images** - Event photos with fallback placeholders

### Layout Improvements:
- **Card-based design** - Each event in its own card
- **Proper alignment** - Left-aligned text, right-aligned buttons
- **Visual hierarchy** - Event name prominent, details secondary
- **Responsive sizing** - Components scale appropriately

---

## 🔧 Technical Implementation Details

### Image Handling:
```java
ImageIcon originalIcon = new ImageIcon(event.getImagePath());
Image scaledImage = originalIcon.getImage().getScaledInstance(180, 120, Image.SCALE_SMOOTH);
```
**Explanation:** Loads and scales images to fit the design

### Price Calculation:
```java
public static BigDecimal calculateTotalPrice(BigDecimal basePrice, String seatType, int quantity)
```
**Explanation:** Multiplies base price by seat type multiplier and quantity

### Event Listeners:
```java
bookButton.addActionListener(new ActionListener() {
    @Override
    public void actionPerformed(ActionEvent e) {
        mainFrame.showBookingForm(event.getId());
    }
});
```
**Explanation:** Anonymous inner class implementing ActionListener interface

---

## 🎤 Presentation Tips

### Code Walkthrough Strategy:
1. **Start with Main.java** - Show application entry point
2. **Explain MVC pattern** - How classes interact
3. **Demonstrate Event model** - OOP concepts
4. **Show GUI components** - Swing usage
5. **Explain event handling** - User interactions
6. **Discuss data flow** - From user input to display

### Common Questions & Answers:

**Q: "Why did you choose this architecture?"**
A: "MVC separates concerns - Event/Booking are models, Panels are views, BookingController handles business logic"

**Q: "How do you handle data persistence?"**
A: "Currently using in-memory ArrayLists for simplicity, but could easily extend to database storage"

**Q: "What design patterns did you use?"**
A: "MVC for architecture, Observer pattern for event handling, and Factory pattern for creating UI components"

**Q: "How do you ensure data validation?"**
A: "Input validation in BookingFormPanel checks for empty fields, valid email format, and seat availability"

**Q: "What would you improve?"**
A: "Add database persistence, more sophisticated error handling, unit tests, and additional features like payment processing"

### Demonstration Flow:
1. **Open project in IntelliJ IDEA**
2. **Run Main.java from the IDE**
3. **Show running application**
4. **Navigate through all three modules**
5. **Create a sample booking**
6. **Explain code for each action**
7. **Highlight OOP concepts used**

---

## 📊 Assessment Criteria Alignment

### Requirements Compliance (5%):
✅ Three modules: Home, Form, Receipt
✅ Required Swing components used
✅ Professional GUI design

### Functionality (15%):
✅ Event browsing works
✅ Booking form validation
✅ Price calculation
✅ Receipt generation

### Creativity (20%):
✅ Professional design with images
✅ Hover effects and visual feedback
✅ Emoji icons for better UX
✅ Responsive layout

### Presentation (50%):
✅ Clear code structure
✅ Comprehensive comments
✅ Demonstrable OOP concepts
✅ Professional appearance

### Teamwork (5%):
✅ Well-organized code structure
✅ Consistent coding style

---

## 🚀 Final Preparation Checklist

- [ ] Practice explaining each class purpose
- [ ] Understand all import statements
- [ ] Know how each GUI component works
- [ ] Prepare to explain OOP concepts
- [ ] Practice running Main.java in IntelliJ IDEA
- [ ] Review error handling approaches
- [ ] Understand the MVC pattern implementation
- [ ] Be ready to discuss potential improvements

**Remember:** Focus on explaining the code line-by-line and demonstrating your understanding of Java OOP concepts!

---

## 🎯 How to Run for Presentation

### **Step-by-Step IntelliJ Setup:**
1. **Open IntelliJ IDEA**
2. **File** → **Open** → Select your `MiniTicketSystem` folder
3. **Wait for indexing** (IntelliJ will scan and configure the project)
4. **Navigate to Main.java** in the project explorer
5. **Right-click Main.java** → "Run 'Main.main()'"
   - OR click the green play button next to `public static void main`
6. **Application starts** - show the lecturer the professional GUI
7. **Explain each .java file** as you walk through the code

### **What IntelliJ Does Automatically:**
- Recognizes the project structure
- Sets up the Java SDK
- Creates run configuration for Main class
- Compiles .java files to .class files in background
- Handles classpath and dependencies

**Simple and Professional!** IntelliJ handles all the technical setup - you focus on explaining your excellent Java code.
