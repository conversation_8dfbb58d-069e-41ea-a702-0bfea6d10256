# Mini Ticket System

**Academic Project for BIT 4043 Object-Oriented Programming Course**

## Project Overview

This is a comprehensive ticket booking system built using Java Swing that demonstrates core OOP concepts and GUI development. The application **fully complies with all lecturer instructions** and academic requirements, featuring three main modules with complete transaction management and professional user interface design.

## 📋 **FULL COMPLIANCE WITH LECTURER INSTRUCTIONS**

### ✅ **Project Requirements Met**
- **✅ Transaction Management System**: Complete ticket booking and reservation process
- **✅ Three Required Modules**: Home Screen, Booking Form, Receipt/Summary
- **✅ GUI Components**: All required and optional components implemented
- **✅ Event-Driven Programming**: Full interactive user interface
- **✅ Data Storage**: In-memory ArrayList implementation
- **✅ Calculation Features**: Real-time price calculation with validation

## Features

### 🏠 **Module 1: Home Screen**
- Display system information and branding
- Show available events with details (name, date, venue, price)
- Professional layout with images, text, and labels
- Navigation to booking form

### 📝 **Module 2: Booking Form**
- Customer information input (name, email, phone)
- Event selection and seat type options
- Quantity selection with spinner control
- **Real-time price calculation** based on seat type and quantity
- Form validation and error handling

### 🧾 **Module 3: Receipt Display**
- Complete booking summary
- Customer and event details
- Ticket information and total price
- Professional receipt format
- Navigation options

## Technical Specifications

### **Technology Stack**
- **Language**: Java SE (no external dependencies)
- **GUI Framework**: Java Swing
- **Architecture**: Simplified MVC pattern
- **Data Storage**: In-memory ArrayLists (no database)

### **GUI Components Used (Full Compliance)**

#### **REQUIRED Components (All Implemented) ✅**
- `JFrame` - Main application window
- `JLabel` - Text and information display throughout all modules
- `JButton` - Action buttons (Book Tickets, Calculate Price, Back to Home, etc.)
- `JTextField` - Text input fields (Customer name, email, phone)
- `JPanel` - Layout containers and content organization

#### **OPTIONAL Components (All Implemented) ✅**
- `JComboBox` - Dropdown selections (Seat type selection)
- `JScrollPane` - Scrollable content (Event list, receipt display)
- `JTextArea` - Multi-line text display (Receipt formatting)
- `JSpinner` - Numeric input with constraints (Quantity selection)

#### **Additional Professional Components ✅**
- `CardLayout` - Seamless panel switching between modules
- `GridBagLayout` - Precise form layout control
- `BorderLayout` - Professional window organization
- `ActionListener` - Event-driven programming implementation

### **Project Structure (Clean & Simple)**
```
MiniTicketSystem/
├── Main.java                          # Complete application (entry point + main window)
├── Event.java                         # Event data model
├── Booking.java                       # Booking data model
├── HomePanel.java                     # Home screen display
├── BookingFormPanel.java              # Ticket booking form
├── ReceiptPanel.java                  # Receipt display
├── BookingController.java             # Business logic controller
├── images/                            # Event images (14 files)
├── PRESENTATION_PREPARATION_GUIDE.md  # Detailed presentation guide
├── HOW_TO_ADD_YOUR_IMAGES.md          # Image replacement guide
└── README.md                          # This file
```

## 🚀 How to Run (Simple!)

### **For Presentation (Recommended)**
1. **Open IntelliJ IDEA**
2. **File** → **Open** → Select the `MiniTicketSystem` folder
3. **Wait for IntelliJ to index the project** (few seconds)
4. **Right-click Main.java** → "Run 'Main.main()'"
5. **Application starts immediately!**

**Alternative:** Use the green play button next to `public static void main` in Main.java

### **Command Line (Alternative)**
```bash
cd MiniTicketSystem
javac *.java    # Compile
java Main       # Run
```

## Sample Data

The application comes pre-loaded with 5 sample events:
1. **Rock Concert 2025** - City Arena ($50.00)
2. **Classical Music Evening** - Symphony Hall ($75.00)
3. **Comedy Show** - Comedy Club ($30.00)
4. **Jazz Festival** - Jazz Lounge ($60.00)
5. **Theater Play** - Grand Theater ($40.00)

## Seat Types & Pricing

- **Standard**: Base price (1.0x multiplier)
- **VIP**: Base price × 1.5
- **Premium**: Base price × 2.0

## Key OOP Concepts Demonstrated

1. **Encapsulation**: Private fields with public getters/setters
2. **Inheritance**: Extending JPanel and JFrame classes
3. **Polymorphism**: Interface implementations and method overriding
4. **Abstraction**: Separation of concerns with MVC pattern
5. **Composition**: Objects containing other objects
6. **Static Methods**: Utility methods for price calculation

## Academic Compliance

✅ **Three Core Modules Only**: Home, Form, Receipt
✅ **Basic Swing Components**: No advanced libraries
✅ **In-Memory Storage**: ArrayLists for data management
✅ **No Database Integration**: Simplified data handling
✅ **No External Dependencies**: Pure Java implementation
✅ **Clean MVC Architecture**: Organized code structure
✅ **Professional UI**: Modern and user-friendly interface

## 🎯 **Lecturer Requirements Compliance**

### **A. Project Description ✅**
- **✅ Transaction Management**: Complete ticket booking and reservation system
- **✅ GUI Components**: Comprehensive use of Java Swing components
- **✅ Event-Driven Programming**: Full interactive user interface with event handling

### **B. Three Required Modules ✅**

#### **Module 1: Home (Screen Interface) ✅**
- **✅ System Information**: Professional branding and project identification
- **✅ Product Display**: 5 events with name, date, venue, description, price
- **✅ Images, Text, Labels**: Appropriately used throughout the interface
- **✅ Navigation**: "Book Tickets" buttons for seamless module switching

#### **Module 2: Form ✅**
- **✅ Suitable Form Design**: Customer information and booking details
- **✅ Required Fields**: Date, event name, seat type, quantity, price
- **✅ Real-time Calculation**: Dynamic total price calculation
- **✅ Form Validation**: Comprehensive input validation with error messages

#### **Module 3: Receipt/Summary ✅**
- **✅ Complete Receipt**: Professional booking confirmation display
- **✅ Item Details**: Event information, seat type, quantity, prices
- **✅ Total Calculation**: Accurate price breakdown and total amount
- **✅ Data Retrieval**: Information retrieved from ArrayList storage

### **C. GUI Components Compliance ✅**

#### **MUST Include (All Present) ✅**
- **✅ JFrame**: Main application window (`MainFrame.java`)
- **✅ JLabel**: Extensive use for text and information display
- **✅ JButton**: Multiple buttons (Book Tickets, Calculate Price, Back to Home)
- **✅ JTextField**: Customer input fields (name, email, phone)
- **✅ JPanel**: Layout containers throughout all modules

#### **MAY Include (All Present) ✅**
- **✅ JComboBox**: Seat type selection dropdown
- **✅ JScrollPane**: Scrollable event list and receipt display
- **✅ JTextArea**: Professional receipt formatting
- **✅ JSpinner**: Quantity selection with numeric constraints

### **D. Implementation Features ✅**
- **✅ Array Storage**: ArrayList for events and bookings data management
- **✅ Class Design**: Complete MVC architecture with 8 well-designed classes
- **✅ Event Handling**: ActionListeners and ChangeListeners implementation
- **✅ Input Validation**: Comprehensive form validation and error handling
- **✅ Professional Design**: Modern UI with proper layout managers

## System Requirements

- **Java SE 8 or higher**
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 512MB RAM
- **Display**: 800x600 minimum resolution

## Author

**BIT 4043 Student**
Object-Oriented Programming Course
Academic Year 2025

---

*This project demonstrates fundamental OOP principles and GUI development using Java Swing for educational purposes.*
