
import javax.swing.*;
import java.awt.*;
import java.io.File;

/**
 * Mini Ticket System - Complete Application
 * Academic project for BIT 4043 Object-Oriented Programming course
 *
 * Beautiful, modern ticket booking system with cohesive design
 * <AUTHOR> 4043 Student
 * @version 2.0
 */
public class Main extends J<PERSON>rame {

    // Modern Color Palette - Professional Blue Theme
    public static final Color PRIMARY_DARK = new Color(25, 42, 86);      // Deep navy blue
    public static final Color PRIMARY_BLUE = new Color(52, 73, 94);      // Professional blue
    public static final Color ACCENT_BLUE = new Color(74, 144, 226);     // Bright blue
    public static final Color SUCCESS_GREEN = new Color(46, 204, 113);   // Success green
    public static final Color TEXT_DARK = new Color(44, 62, 80);         // Dark text
    public static final Color TEXT_LIGHT = new Color(127, 140, 141);     // Light text
    public static final Color BACKGROUND = new Color(248, 249, 250);     // Light background
    public static final Color CARD_WHITE = Color.WHITE;                  // Pure white cards

    // Application components
    private CardLayout cardLayout;
    private JPanel contentPanel;
    private BookingController bookingController;

    // Panel references
    private HomePanel homePanel;
    private BookingFormPanel bookingFormPanel;
    private ReceiptPanel receiptPanel;

    // Card names
    public static final String HOME_CARD = "HOME";
    public static final String BOOKING_CARD = "BOOKING";
    public static final String RECEIPT_CARD = "RECEIPT";

    /**
     * Main method to start the application
     */
    public static void main(String[] args) {
        // Set modern look and feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            System.out.println("Using default look and feel.");
        }

        // Create and display the application
        SwingUtilities.invokeLater(() -> {
            try {
                Main app = new Main();
                app.setVisible(true);
                System.out.println("Mini Ticket System started successfully!");
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null,
                    "Error starting application: " + e.getMessage(),
                    "Startup Error", JOptionPane.ERROR_MESSAGE);
            }
        });
    }

    /**
     * Constructor - Initialize the application
     */
    public Main() {
        bookingController = new BookingController();
        initializeFrame();
        initializePanels();
        setupLayout();
    }

    /**
     * Initialize the main frame
     */
    private void initializeFrame() {
        setTitle("Mini Ticket System - BIT 4043 OOP Project");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(900, 700);
        setLocationRelativeTo(null);
        setResizable(true);

        // Set beautiful background
        getContentPane().setBackground(BACKGROUND);

        // Set application icon
        try {
            File iconFile = new File("images/icon.png");
            if (iconFile.exists()) {
                setIconImage(Toolkit.getDefaultToolkit().createImage("images/icon.png"));
            }
        } catch (Exception e) {
            // Ignore if no icon available
        }
    }

    /**
     * Initialize all panels
     */
    private void initializePanels() {
        homePanel = new HomePanel(this, bookingController);
        bookingFormPanel = new BookingFormPanel(this, bookingController);
        receiptPanel = new ReceiptPanel(this, bookingController);
    }

    /**
     * Setup the layout with CardLayout
     */
    private void setupLayout() {
        cardLayout = new CardLayout();
        contentPanel = new JPanel(cardLayout);
        contentPanel.setBackground(BACKGROUND);

        // Add panels to card layout
        contentPanel.add(homePanel, HOME_CARD);
        contentPanel.add(bookingFormPanel, BOOKING_CARD);
        contentPanel.add(receiptPanel, RECEIPT_CARD);

        // Add content panel to frame
        add(contentPanel, BorderLayout.CENTER);

        // Show home panel initially
        showCard(HOME_CARD);
    }

    // Navigation methods
    public void showCard(String cardName) {
        cardLayout.show(contentPanel, cardName);
    }

    public void showHome() {
        homePanel.refreshEventList();
        showCard(HOME_CARD);
    }

    public void showBookingForm(int eventId) {
        bookingFormPanel.setSelectedEvent(eventId);
        showCard(BOOKING_CARD);
    }

    public void showReceipt(Booking booking) {
        receiptPanel.displayBooking(booking);
        showCard(RECEIPT_CARD);
    }

    public BookingController getBookingController() {
        return bookingController;
    }
}
